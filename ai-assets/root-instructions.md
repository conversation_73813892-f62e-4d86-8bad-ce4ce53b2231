- check ./repository.md for general guideliness of the current workspace. Keep in mind this is an nx monorepo which means that you should run any commands like build or test through npx nx.

- DO NOT write new test cases unless the prompt explicitly tells you to

- DO NOT export anything if its not yet used anywhere, e.g. exporting each new class you created in a library

- restrain from adding too many code comments, especially if they're explaining "what we're doing" instead of "why we're doing it"

- use the "sequentialthinking" MCP for more challenging reasoning tasks

- use the "context7" MCP for tasks that require a lot of context and background information about libraries

- use the "memory" MCP proactively to store or retrieve memories which might be useful either for the current task or for future ones

- when told to use "feature planning" - you MUST read the `./feature-planning.md` file as your point of entry for further instructions.

- when planning feature work - proactively ask the human for additonal information where needed

- when doing diagnostics - prioritize linting and type checking first, before running tests or builds as the latter take longer to execute. Tests and builds can come as a final step after fixing any linting and type errors.

- when trying to complete tasks or fix issues, give up early after maximum of 3-4 retries and instead ask the human for help while providing any helpful thoughts

- proactively try to use your own built in "todo list" / "task list" when planning work

-after completing tasks or doing analysis - you could propose updates or general improvements to the files in the root `ai-assets` folder

- you can find information about the product itself in `./codashi.md` if needed

- you can find repository organization info in `./repository.md`

- you must adhere to `./general.guide.md` for all the code you write

- if you're doing front-end work you must also check `./front-end.guide`

- if you're doing back-end work you must also check `./back-end.guide`

- if the task I just was repetitive and might be needed again, you should suggest: "Would you like me to add this task to the memory bank for future reference?"
