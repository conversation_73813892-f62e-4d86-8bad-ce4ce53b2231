- use snake_case for fields in JSON schemas or database fields. camelCase for regular code variables

- use kebab-case for file and folder names

- use custom error classes when designing internal logic and services. For boundary-level surfaces like an endpoint or react-router action errors (program inputs and outputs) - return one of my custom errors from @awe/core - Parsing<PERSON>rror, DomainError, UnexpectedError.

- Always use `type` instead of `interface`

- Use `import { type SomeType }` when importing types

- use function expressions instead of function declarations

- use zod parsers and derive types from them for all inputs and outputs, and use the `zodToDomainOrThrow` function to convert from zod to domain types.

- react-router actions/loaders - use separate service layer inside them instead of inlining the logic

- if a function or method's arguments become more than 3 - refactor them into an object instead

---

# Engineering Best Practices

## Type Safety & Validation

1. **Prefer Strict Types**  
   Use TypeScript's type system as the first line of defense:

   - Define precise types for all inputs/outputs
   - Use utility types (`Partial`, `Pick`, etc.) for complex scenarios
   - Leverage branded types for domain-specific constraints

2. **Runtime Checks**  
   Only add runtime validation when:
   - Handling external/untrusted input (APIs, user input, etc.)
   - Dealing with type boundaries (e.g., API endpoints)
   - Checking complex invariants that can't be expressed in types

## Type Casting & Any Usage

1. **Avoid Type Assertions**  
   Prefer type inference and proper typing over `as` assertions:

   ```typescript
   // Bad ❌
   const value = JSON.parse(raw) as MyType;

   // Better ✅
   const value = parseMyType(raw); // Uses Zod validator
   ```

2. **Ban `any` Type**  
   Never use `any` unless absolutely unavoidable:

   ```typescript
   // Bad ❌
   function dangerous(data: any) { ... }

   // Better ✅
   function safe(data: unknown) {
     if (isValid(data)) { ... } // Proper type guard
   }
   ```

3. **Exception Justification**  
   When forced to use `any` or type assertions:
   - Add a `// @ts-expect-error` comment explaining why
   - Document the rationale in a code comment
   - Include ticket number if addressing tech debt
   ```typescript
   // Temporary workaround for SDK types mismatch (#PROJ-123)
   const response = (await sdk.call()) as unknown as CorrectType;
   ```

## Validation Layers

```typescript
// Good for external data
const validateInput = (input: unknown) => {
  return schema.parse(input); // Using Zod
};

// Unnecessary for internal type-safe code
function processUser(user: User) {
  // No need to check user properties - type system guarantees them
}
```

## Example: TitleMatchAnalyzer

```typescript
// Before: Runtime checks + type annotations
validateInputs(resumeVariations, job);

// After: Type-driven approach
function findBestTitleMatch(
  resumeVariations: Resume[], // Type enforces array structure
  job: Job // Type ensures valid Job object
) {
  // No runtime checks needed - type system enforces constraints
}
```
