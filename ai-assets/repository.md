# Project Overview

**AWE Labs Monorepo**

Next-generation career development platform focused on AI-driven job matching and skill analysis. Provides tools for resume parsing, competency matching, and personalized career path recommendations.

**Core Functionality:**

- Automated extraction of skills/experience from resumes and job descriptions
- AI-powered matching between candidate profiles and job opportunities
- Interactive tools for career development planning

**Primary Technologies:**

- Nx Monorepo
- TypeScript
- React/Remix
- Cloudflare Workers
- PostgreSQL
- Playwright (E2E testing)

# Monorepo Structure

## Active Projects

### Applications

- **codashi** (`apps/codashi`)

  Current production system combining resume analysis, job matching, and career planning tools

  Tech stack intention:

  react-router 7 in framework mode
  react-pdf
  react-email + resend
  zod
  modern vanilla css, both with semantic and utility classes
  postgres, drizzle
  webllm for in-browser inference
  langchain.js
  runs within cloudflare workers

### Libraries

- **awe-core** (`libs/core`)

  Isomorphic core library for all AWE projects, containing shared types, utilities, and error classes

- **codashi-core** (`libs/codashi-core`)

  Core business logic for resume parsing, skill matching, and job recommendation algorithms

- **codashi-ui** (`libs/codashi-ui`)

  Shared UI components and design system for career development interfaces

## Deprecated Projects

### codarashi (`apps/codarashi`)

- Retained for reference on:
  - Complex form handling patterns
  - E2E testing strategies

### codarashi-hq (`apps/codarashi-hq`)

- Original skills ingestion and management system
- Valuable for:
  - Complex relation modeling
  - Astro-based admin interface examples

## Shared Infrastructure

- Nx workspace configuration (`.nx`)
- ESLint/Prettier standards (`.eslintrc.json`, `.prettierrc`)
- Unified CI/CD pipelines (`.github`)
