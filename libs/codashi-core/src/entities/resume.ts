import { z } from 'zod';

import { Deep<PERSON>artial, isoDate<PERSON><PERSON><PERSON>, zodToDomainOrThrow } from '@awe/core';

const markdownString = z
  .string()
  .describe(
    'Markdown content with optional template variables using {{ varName }} syntax'
  );

const socialProfile = z.object({
  network: z.string(),
  username: z.string().nullable(),
  url: z.string().url(),
});

const basicsSchema = z.object({
  name: z.string(),
  title: z.string().describe('Professional title or role'),
  image: z.string().nullable().default(null),
  email: z.string().email(),
  phone: z.string().nullable().default(null),
  url: z
    .string()
    .url()
    .nullable()
    .default(null)
    .describe('Personal website or blog URL'),

  location: z
    .object({
      country_code: z.string(),
      address: z.string().nullable(),
      postal_code: z.string().nullable(),
      city: z.string().nullable(),
      region: z.string().nullable(),
    })
    .nullable()
    .default(null),
  profiles: z
    .array(socialProfile)
    .nullable()
    .default([])
    .describe('Social media and online profiles'),
  summary: markdownString
    .nullable()
    .default(null)
    .describe(
      'Brief summary or bio - supports Markdown with template variables'
    ),
});

export const referenceSchema = z.object({
  name: z.string(),
  reference: z.string().nullable(),
});

const metaSchema = z.object({
  // URL to the canonical version of this resume
  canonical: z.string().url().nullable(),
  // Schema version or resume format version
  version: z.string().nullable(),
  // When the resume was last updated
  last_modified: z.string().nullable(),
});

// Define the main resume schema - follows the JSON Resume standard format
// See: https://jsonresume.org/schema/
const jsonResume = z.object({
  // Reference to the JSON Schema that validates this document
  $schema: z.string().url().nullable().default(null),
  meta: metaSchema.nullable(),
  basics: basicsSchema.nullable().default(null),

  skills: z.array(
    z.object({
      // Primary skill name (e.g., "JavaScript", "Project Management")
      name: z.string(),
      // Proficiency level (e.g., "Beginner", "Intermediate", "Expert")
      level: z.string().nullable(),
      // Related sub-skills, libraries, or specific technologies
      // For example, for "JavaScript" might include ["React", "Node.js", "TypeScript"]
      keywords: z.array(z.string()).nullable(),
    })
  ),

  projects: z.array(
    z.object({
      name: z.string(),
      // Project description - supports Markdown with template variables
      description: markdownString,
      // Specific accomplishments or contributions to the project
      highlights: z.array(z.string()).nullable(),
      // Technical skills, tools, and technologies used
      // These can be mapped to the skills taxonomy for matching
      keywords: z.array(z.string()).nullable(),
      start_date: isoDateParser,
      end_date: isoDateParser.nullable(),
      url: z.string().url().nullable(),
      // Roles or positions held within the project
      roles: z.array(z.string()).nullable(),
      // Organization or company associated with the project
      entity: z.string().nullable(),
      // Project category (e.g., "Open Source", "Academic", "Commercial")
      type: z.string(),
    })
  ),

  references: z.array(referenceSchema),

  languages: z.array(
    z.object({
      language: z.string(),
      fluency: z.string(),
    })
  ),

  interests: z.array(
    z.object({
      name: z.string(),
      keywords: z.array(z.string()).nullable(),
    })
  ),

  publications: z.array(
    z.object({
      name: z.string(),
      publisher: z.string().nullable(),
      release_date: isoDateParser,
      url: z.string().url().nullable(),
      summary: z.string(),
    })
  ),

  certificates: z.array(
    z.object({
      name: z.string(),
      date: isoDateParser,
      url: z.string().url().nullable(),
      issuer: z.string(),
    })
  ),

  awards: z.array(
    z.object({
      title: z.string(),
      date: isoDateParser,
      awarder: z.string(),
      summary: z.string(),
    })
  ),

  work: z.array(
    z.object({
      // Company or organization name
      name: z.string(),
      location: z.string().nullable().default(null),
      // Company description or industry information
      description: markdownString.nullable().default(null),
      // Job title or role
      position: z.string(),
      url: z.string().url().nullable().default(null),
      start_date: isoDateParser,
      // Null/undefined indicates current position
      end_date: isoDateParser.nullable().default(null),
      // Job summary - supports Markdown with template variables
      summary: markdownString.nullable().default(null),
      // Key accomplishments as bullet points
      highlights: z.array(z.string()).nullable().default(null),
    })
  ),

  volunteer: z.array(
    z.object({
      position: z.string(),
      summary: z.string(),
      start_date: isoDateParser,

      url: z.string().url().nullable(),
      organization: z.string().nullable(),
      end_date: isoDateParser.nullable(),
      highlights: z.array(z.string()).nullable(),
    })
  ),

  education: z.array(
    z.object({
      institution: z.string(),
      area: z.string(),

      url: z.string().url().nullable(),
      study_type: z.string().nullable(),
      start_date: isoDateParser,
      end_date: isoDateParser.nullable(),
      score: z.string().nullable(),
      courses: z.array(z.string()).nullable(),
    })
  ),
});

// superset of the json resume schema - our software's assumptions on what
// deserves to be done deeper
const extendedResume = jsonResume.merge(
  z.object({
    meta: metaSchema.extend({}).nullable().default(null),
    references: z.array(
      referenceSchema.extend({
        email: z.string().email().nullable(),
        phone: z.string().nullable(),
        position: z.string().nullable(),
        company: z.string().nullable(),
      })
    ),

    work: z.array(
      jsonResume.shape.work.element.and(
        z.object({
          // This allows showing career progression within a company
          positions: z
            .array(
              z.object({
                position: z.string().describe('Job title or role'),
                start_date: isoDateParser.describe('When the position started'),
                end_date: isoDateParser
                  .nullable()
                  .describe('When the position ended, null if current'),
                summary: markdownString.describe(
                  'Brief description of responsibilities in this role - supports Markdown with template variables'
                ),
                highlights: z
                  .array(z.string())
                  .nullable()
                  .describe('Key accomplishments specific to this role'),
                time_allocation: z
                  .number()
                  .min(0)
                  .max(100)
                  .nullable()
                  .describe('Percentage of time allocated to this role'),
              })
            )
            .nullable()
            .default(null),
        })
      )
    ),
  })
);

// for now its not going to be something we save to the DB, keeping it here in case
// we decide to build it in-memory for things like suggestions
export const profile = z.object({
  basics: basicsSchema.merge(
    z.object({
      // from the basics fields - we may want to have multiple versions of our title
      // and summary as those are important for positioning ourselves in the context
      // of a job description
      title: z.array(basicsSchema.shape.title).min(1),
      summary: z.array(basicsSchema.shape.summary).min(1),
    })
  ),

  skills: extendedResume.shape.skills.min(1),
  work: extendedResume.shape.work.default([]),
  projects: extendedResume.shape.projects.default([]),
  education: extendedResume.shape.education.default([]),
  languages: extendedResume.shape.languages.default([]),
  interests: extendedResume.shape.interests.default([]),
  references: extendedResume.shape.references.default([]),
  publications: extendedResume.shape.publications.default([]),
  certificates: extendedResume.shape.certificates.default([]),
  awards: extendedResume.shape.awards.default([]),
  volunteer: extendedResume.shape.volunteer.default([]),

  // Meta information about the profile
  meta: metaSchema.nullable().default(null),
  $schema: extendedResume.shape.$schema.nullable().default(null),
});

export const resume = z
  .object({
    // these items are re-orderable thus we represent the header as an array
    $schema: jsonResume.shape.$schema.nullable().default(null),
    meta: metaSchema.and(
      z.object({
        job_description: z.string().nullable().default(null),
        created_at: isoDateParser,
      })
    ),

    header: z.object({
      // we may add additional fields here, for example - layout, centered, left aligned
      // and so on
      items: z.array(
        z.union([
          z.object({
            name: z.literal('name'),
            value: basicsSchema.shape.name,
          }),
          z.object({
            name: z.literal('title'),
            value: basicsSchema.shape.title,
          }),
          z.object({
            name: z.literal('email'),
            value: basicsSchema.shape.email,
          }),
          z.object({
            name: z.literal('phone'),
            value: basicsSchema.shape.phone,
          }),
          z.object({
            name: z.literal('url'),
            value: basicsSchema.shape.url,
          }),
          z.object({
            name: z.literal('location'),
            value: basicsSchema.shape.location,
          }),
          z.object({
            name: z.literal('summary'),
            value: basicsSchema.shape.summary,
          }),
          z.object({
            name: z.literal('profile'),
            value: socialProfile,
          }),
        ])
      ),
    }),
  })
  .merge(
    z.object({
      sections: z.array(
        z.discriminatedUnion('name', [
          z.object({
            name: z.literal('work'),
            items: extendedResume.shape.work,
          }),
          z.object({
            name: z.literal('volunteer'),
            items: extendedResume.shape.volunteer,
          }),
          z.object({
            name: z.literal('education'),
            items: extendedResume.shape.education,
          }),
          z.object({
            name: z.literal('awards'),
            items: extendedResume.shape.awards,
          }),
          z.object({
            name: z.literal('certificates'),
            items: extendedResume.shape.certificates,
          }),
          z.object({
            name: z.literal('publications'),
            items: extendedResume.shape.publications,
          }),
          z.object({
            name: z.literal('skills'),
            items: extendedResume.shape.skills,
          }),
          z.object({
            name: z.literal('languages'),
            items: extendedResume.shape.languages,
          }),
          z.object({
            name: z.literal('interests'),
            items: extendedResume.shape.interests,
          }),
          z.object({
            name: z.literal('references'),
            items: extendedResume.shape.references,
          }),
          z.object({
            name: z.literal('projects'),
            items: extendedResume.shape.projects,
          }),
        ])
      ),
    })
  );

export type Resume = z.infer<typeof resume>;

type SectionNames = Resume['sections'][number]['name'];

export type ResumeSection<T extends SectionNames> = Extract<
  Resume['sections'][number],
  { name: T }
>;

export type ResumeEntity<T extends 'work' | 'project' | 'education'> =
  T extends 'work'
    ? ResumeSection<'work'>['items'][number]
    : T extends 'project'
    ? ResumeSection<'projects'>['items'][number]
    : T extends 'education'
    ? ResumeSection<'education'>['items'][number]
    : never;

export const toResume = {
  fromPartial: (data: DeepPartial<Resume>) => zodToDomainOrThrow(resume, data),
};

/**
 * Notes on rich text fields:
 *
 * Fields using markdownString support Markdown formatting with the following features:
 * - Standard Markdown syntax (headings, lists, links, emphasis, etc.)
 * - Template variables using {{ varName }} syntax for dynamic content
 * - Limited HTML tags for additional formatting when needed
 *
 * Example:
 * ```markdown
 * ## Work Experience at {{company}}
 *
 * During my time at {{company}}, I accomplished:
 * - Increased performance by **40%**
 * - Led a team of {{teamSize}} developers
 * - Implemented <span class="highlight">key features</span>
 * ```
 *
 */
