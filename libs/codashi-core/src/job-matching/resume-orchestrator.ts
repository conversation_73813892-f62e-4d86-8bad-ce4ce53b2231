import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import type { Job } from '../entities/job';
import type { Resume } from '../entities/resume';
import { ExperienceAnalyzer } from './experience/analyzer';
import { ExperienceAnalysis } from './experience/types';
import { ProjectAnalyzer } from './projects';
import type { ProjectAnalysis } from './projects/types';
import { SkillAnalyzer } from './skills/analyzer';
import { SkillAnalysis } from './skills/types';
import { TitleAnalyzer } from './title/analyzer';
import { type TitleAnalysis } from './title/utils';
import { AnalysisOptions } from './types';

/**
 * Combined result type for all single-resume analyses
 */
type AnalysisResult = {
  /** Experience analysis result */
  experience: ExperienceAnalysis;
  /** Skills analysis result */
  skills: SkillAnalysis;
  /** Title analysis result */
  title: TitleAnalysis;
  /** Projects analysis result */
  projects: ProjectAnalysis;
  /** Overall summary across all analysis types */
  overallSummary: {
    /** Total score combining all analysis types (0-100) */
    totalScore: number;
    /** Overall match quality: 'poor', 'fair', 'good', 'excellent' */
    matchQuality: 'poor' | 'fair' | 'good' | 'excellent';
    /** Key strengths identified */
    strengths: string[];
    /** Key areas for improvement */
    improvements: string[];
    /** Whether this resume is recommended for the job */
    isRecommended: boolean;
  };
};

/**
 * Orchestrates complete analysis of a single resume against a job posting.
 *
 * This class performs comprehensive analysis including:
 * - Experience matching and scoring
 * - Skills analysis (direct, transferable, missing)
 * - Title matching and optimization
 * - Projects relevance scoring
 * - Overall recommendation and summary
 */
export class ResumeOrchestrator {
  private model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  /**
   * Analyzes a single resume against a job posting.
   *
   * @param resume - Single resume to analyze
   * @param job - Job posting to match against
   * @param options - Optional configuration for all analysis types
   * @returns Promise resolving to complete resume analysis result
   *
   * @example
   * ```typescript
   * const orchestrator = new ResumeOrchestrator(mistralModel);
   * const analysis = await orchestrator.analyzeResume(
   *   resume,
   *   jobPosting,
   *   {
   *     includeSourceDetails: true,
   *     experience: { includeImprovements: true },
   *     skills: { confidenceThreshold: 2 },
   *     title: { suggestImprovements: true }
   *   }
   * );
   *
   * console.log(`Overall score: ${analysis.overallSummary.totalScore}`);
   * console.log(`Match quality: ${analysis.overallSummary.matchQuality}`);
   * console.log(`Recommended: ${analysis.overallSummary.isRecommended}`);
   * ```
   */
  public async analyzeResume(
    resume: Resume,
    job: Job,
    options: AnalysisOptions = {}
  ): Promise<AnalysisResult> {
    try {
      const finalOptions = {
        includeSourceDetails: false,
        timeoutMs: 60000, // 1 minute total timeout
        ...options,
      };

      // Run all four analyses in parallel for efficiency
      const [
        experienceAnalysis,
        skillsAnalysis,
        titleAnalysis,
        projectsAnalysis,
      ] = await Promise.all([
        new ExperienceAnalyzer(this.model).analyzeExperience(
          resume,
          job,
          finalOptions.experience
        ),
        new SkillAnalyzer(this.model).analyzeSkills(
          resume,
          job,
          finalOptions.skills
        ),
        new TitleAnalyzer(this.model).analyzeTitle(
          resume,
          job,
          finalOptions.title
        ),
        new ProjectAnalyzer(this.model).analyzeProjects(
          resume,
          job,
          finalOptions.projects
        ),
      ]);

      // Calculate overall summary
      const overallSummary = this.calculateOverallSummary(
        experienceAnalysis,
        skillsAnalysis,
        titleAnalysis,
        projectsAnalysis
      );

      return {
        experience: experienceAnalysis,
        skills: skillsAnalysis,
        title: titleAnalysis,
        projects: projectsAnalysis,
        overallSummary,
      };
    } catch (error) {
      throw new Error(
        `Failed to analyze single resume: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Analyzes multiple resumes against a single job posting.
   *
   * This function processes multiple resumes individually and returns
   * an array of complete analysis results, maintaining the order of input resumes.
   *
   * @param resumes - Array of resumes to analyze
   * @param job - Job posting to match against
   * @param options - Optional configuration for all analysis types
   * @returns Promise resolving to array of resume analysis results
   *
   * @example
   * ```typescript
   * const orchestrator = new ResumeOrchestrator(mistralModel);
   * const analyses = await orchestrator.analyzeMultipleResumes(
   *   [resume1, resume2, resume3],
   *   jobPosting,
   *   { skills: { confidenceThreshold: 2 } }
   * );
   *
   * // Sort by overall score
   * const ranked = analyses.sort((a, b) =>
   *   b.overallSummary.totalScore - a.overallSummary.totalScore
   * );
   *
   * console.log(`Best candidate score: ${ranked[0].overallSummary.totalScore}`);
   * ```
   */
  public async analyzeMultipleResumes(
    resumes: Resume[],
    job: Job,
    options: AnalysisOptions = {}
  ): Promise<AnalysisResult[]> {
    if (!resumes || resumes.length === 0) {
      throw new Error('At least one resume is required');
    }

    // Process resumes sequentially to avoid overwhelming the AI model
    const results: AnalysisResult[] = [];

    for (const resume of resumes) {
      try {
        const analysis = await this.analyzeResume(resume, job, options);
        results.push(analysis);
      } catch (error) {
        console.warn(
          `Failed to analyze resume, skipping: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`
        );
        // Continue with other resumes rather than failing completely
      }
    }

    return results;
  }

  /**
   * Calculates overall summary combining all analysis types
   */
  private calculateOverallSummary(
    experienceAnalysis: AnalysisResult['experience'],
    skillsAnalysis: AnalysisResult['skills'],
    titleAnalysis: AnalysisResult['title'],
    projectsAnalysis: AnalysisResult['projects']
  ): AnalysisResult['overallSummary'] {
    // Calculate component scores
    const experienceScore = this.calculateExperienceScore(experienceAnalysis);
    const skillsScore = skillsAnalysis.summary.coveragePercentage;
    const titleScore = titleAnalysis.confidence * 100;
    const projectsScore = this.calculateProjectsScore(projectsAnalysis);

    // Calculate weighted total score (experience 35%, skills 35%, title 15%, projects 15%)
    const totalScore = Math.round(
      experienceScore * 0.35 +
        skillsScore * 0.35 +
        titleScore * 0.15 +
        projectsScore * 0.15
    );

    // Determine match quality
    const matchQuality = this.calculateMatchQuality(
      experienceScore,
      skillsScore,
      titleAnalysis.confidence,
      projectsScore
    );

    // Determine recommendation
    const isRecommended = this.shouldRecommendResume(
      matchQuality,
      titleAnalysis.confidence,
      skillsScore
    );

    // Identify strengths and improvements
    const strengths = this.identifyStrengths(
      experienceAnalysis,
      skillsAnalysis,
      titleAnalysis,
      projectsAnalysis
    );
    const improvements = this.identifyImprovements(
      experienceAnalysis,
      skillsAnalysis,
      titleAnalysis,
      projectsAnalysis
    );

    return {
      totalScore,
      matchQuality,
      strengths,
      improvements,
      isRecommended,
    };
  }

  /**
   * Helper function to calculate overall match quality
   */
  private calculateMatchQuality(
    experienceScore: number,
    skillsCoverage: number,
    titleConfidence: number,
    projectsScore: number
  ): 'poor' | 'fair' | 'good' | 'excellent' {
    // Weighted average: experience 35%, skills 35%, title 15%, projects 15%
    const overallScore =
      experienceScore * 0.35 +
      skillsCoverage * 0.35 +
      titleConfidence * 100 * 0.15 +
      projectsScore * 0.15;

    if (overallScore >= 80) return 'excellent';
    if (overallScore >= 65) return 'good';
    if (overallScore >= 45) return 'fair';
    return 'poor';
  }

  /**
   * Helper function to determine if a resume should be recommended
   */
  private shouldRecommendResume(
    matchQuality: 'poor' | 'fair' | 'good' | 'excellent',
    titleConfidence: number,
    skillsCoverage: number
  ): boolean {
    // Recommend if:
    // - Match quality is good or excellent, OR
    // - Match quality is fair AND (high title confidence OR good skills coverage)
    return (
      matchQuality === 'excellent' ||
      matchQuality === 'good' ||
      (matchQuality === 'fair' &&
        (titleConfidence >= 0.7 || skillsCoverage >= 60))
    );
  }

  /**
   * Calculates a score for experience analysis (0-100)
   */
  private calculateExperienceScore(
    experienceAnalysis: AnalysisResult['experience']
  ): number {
    if (experienceAnalysis.experiences.length === 0) {
      return 0;
    }

    const avgScore = experienceAnalysis.summary.averageRelevanceScore;
    // Convert 1-5 scale to 0-100 scale
    return Math.round(((avgScore - 1) / 4) * 100);
  }

  /**
   * Identifies key strengths from the analysis
   */
  private identifyStrengths(
    experienceAnalysis: AnalysisResult['experience'],
    skillsAnalysis: AnalysisResult['skills'],
    titleAnalysis: AnalysisResult['title'],
    projectsAnalysis: AnalysisResult['projects']
  ): string[] {
    const strengths: string[] = [];

    // Experience strengths
    if (experienceAnalysis.summary.averageRelevanceScore >= 4) {
      strengths.push('Highly relevant work experience');
    }
    if (experienceAnalysis.summary.highRelevanceCount >= 2) {
      strengths.push('Multiple high-quality experiences');
    }

    // Skills strengths
    if (skillsAnalysis.summary.coveragePercentage >= 70) {
      strengths.push('Excellent skill coverage');
    }
    if (skillsAnalysis.summary.directMatchCount >= 5) {
      strengths.push('Strong direct skill matches');
    }

    // Title strengths
    if (titleAnalysis.confidence >= 0.8) {
      strengths.push('Well-aligned job title');
    }

    // Projects strengths
    if (this.calculateProjectsScore(projectsAnalysis) >= 70) {
      strengths.push('Strong relevant project experience');
    }

    return strengths;
  }

  /**
   * Identifies areas for improvement from the analysis
   */
  private identifyImprovements(
    experienceAnalysis: AnalysisResult['experience'],
    skillsAnalysis: AnalysisResult['skills'],
    titleAnalysis: AnalysisResult['title'],
    projectsAnalysis: AnalysisResult['projects']
  ): string[] {
    const improvements: string[] = [];

    // Experience improvements
    if (experienceAnalysis.summary.averageRelevanceScore < 3) {
      improvements.push('Highlight more relevant work experience');
    }

    // Skills improvements
    if (skillsAnalysis.summary.coveragePercentage < 50) {
      improvements.push('Develop missing technical skills');
    }
    if (skillsAnalysis.summary.missingSkillCount > 5) {
      improvements.push('Address key skill gaps');
    }

    // Title improvements
    if (titleAnalysis.suggestedTitle) {
      improvements.push(
        `Consider updating title to: ${titleAnalysis.suggestedTitle}`
      );
    }

    // Projects improvements
    if (this.calculateProjectsScore(projectsAnalysis) < 50) {
      improvements.push(
        'Include more projects aligned with the job requirements'
      );
    }

    return improvements;
  }

  /**
   * Calculates a score for projects analysis (0-100)
   */
  private calculateProjectsScore(
    projectsAnalysis: AnalysisResult['projects']
  ): number {
    if (projectsAnalysis.projects.length === 0) {
      return 0;
    }
    const avgScore = projectsAnalysis.summary.averageRelevanceScore;
    return Math.round(((avgScore - 1) / 4) * 100);
  }
}
