import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
import { ProjectExtractor } from './extractor';
import { ProjectScorer } from './scorer';
import type { ProjectAnalysis, ProjectAnalysisOptions } from './types';

/**
 * Class for analyzing projects matches between a single resume and a job posting.
 */
export class ProjectAnalyzer {
  private readonly model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  async analyzeProjects(
    resume: Resume,
    job: Job,
    options: ProjectAnalysisOptions = {}
  ): Promise<ProjectAnalysis> {
    const finalOptions = {
      timeoutMs: 30000,
      batchSize: 5,
      ...options,
    } satisfies ProjectAnalysisOptions;

    try {
      // Step 1: Extract projects from the resume
      const extractor = new ProjectExtractor();
      const projects = extractor.extractProjects(resume);

      if (projects.length === 0) {
        return {
          projects: [],
          scoredProjects: [],
          summary: {
            totalProjects: 0,
            averageRelevanceScore: 0,
            highRelevanceCount: 0,
          },
        };
      }

      // Step 2: Score projects against job requirements
      const scorer = new ProjectScorer(this.model);
      const scoredProjects = await withTimeout(
        scorer.scoreProjects(projects, job, finalOptions),
        finalOptions.timeoutMs,
        'Single resume projects scoring'
      );

      const summary = this.calculateSummary(scoredProjects);

      return {
        projects,
        scoredProjects,
        summary,
      };
    } catch (error) {
      if (isAIUnavailableError(error)) {
        throw new ProjectMatchError(
          'AI model is currently unavailable for projects analysis',
          'AI_UNAVAILABLE'
        );
      }

      if (isTimeoutError(error)) {
        throw new ProjectMatchError('Projects analysis timed out', 'TIMEOUT');
      }

      throw new ProjectMatchError(
        `Projects analysis failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        'INVALID_INPUT'
      );
    }
  }

  private calculateSummary(
    scoredProjects: ProjectAnalysis['scoredProjects']
  ): ProjectAnalysis['summary'] {
    const totalProjects = scoredProjects.length;
    if (totalProjects === 0) {
      return {
        totalProjects: 0,
        averageRelevanceScore: 0,
        highRelevanceCount: 0,
      };
    }

    const totalScore = scoredProjects.reduce(
      (sum, sp) => sum + sp.relevanceScore,
      0
    );
    const averageRelevanceScore =
      Math.round((totalScore / totalProjects) * 10) / 10;
    const highRelevanceCount = scoredProjects.filter(
      (sp) => sp.relevanceScore >= 4
    ).length;

    return {
      totalProjects,
      averageRelevanceScore,
      highRelevanceCount,
    };
  }
}

class ProjectMatchError extends Error {
  constructor(
    message: string,
    public readonly code:
      | 'AI_UNAVAILABLE'
      | 'TIMEOUT'
      | 'INVALID_INPUT'
      | 'NO_PROJECTS'
  ) {
    super(message);
    this.name = 'ProjectMatchError';
  }
}
