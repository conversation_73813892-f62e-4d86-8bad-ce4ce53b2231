import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Job } from '../../entities/job';
import {
  withTimeout,
  isAIUnavailableError,
  isTimeoutError,
} from '../../utils/common-utils';
import {
  PROJECT_SCORING_STANDALONE_PROMPT,
  formatJobContext,
  formatProjectForPrompt,
} from './prompt-components';
import type {
  ProjectAnalysisOptions,
  ProjectItem,
  ProjectScore,
} from './types';
import { projectScoreSchema, type ProjectScoreResult } from './types';

/**
 * Scores projects from a single resume against job requirements.
 */
export class ProjectScorer {
  private readonly DEFAULT_BATCH_SIZE = 8;
  private readonly MAX_SAFE_TOKENS = 8000;

  constructor(private model: BaseChatModel) {}

  async scoreProjects(
    projects: ProjectItem[],
    job: Job,
    options: ProjectAnalysisOptions = {}
  ): Promise<ProjectScore[]> {
    if (!projects.length) return [];

    const batchSize = options.batchSize || this.DEFAULT_BATCH_SIZE;
    const timeoutMs = options.timeoutMs || 30000;

    try {
      const totalTokens = this.estimateTotalTokens(projects, job);
      if (totalTokens <= this.MAX_SAFE_TOKENS && projects.length <= batchSize) {
        return await this.scoreBatch(projects, job, timeoutMs);
      } else {
        return await this.scoreInBatches(projects, job, batchSize, timeoutMs);
      }
    } catch (error) {
      if (isAIUnavailableError(error) || isTimeoutError(error)) {
        return this.createDefaultScores(projects);
      }
      throw error;
    }
  }

  private async scoreBatch(
    projects: ProjectItem[],
    job: Job,
    timeoutMs: number
  ): Promise<ProjectScore[]> {
    const prompt = ChatPromptTemplate.fromTemplate(
      PROJECT_SCORING_STANDALONE_PROMPT
    );
    const parser = StructuredOutputParser.fromZodSchema(projectScoreSchema);
    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const projectList = projects
      .map((p, index) => formatProjectForPrompt(p, index))
      .join('\n\n');

    const jobContext = formatJobContext(job);

    const result = await withTimeout(
      chain.invoke({
        ...jobContext,
        projectList,
        format_instructions: parser.getFormatInstructions(),
      }),
      timeoutMs,
      'Single resume projects scoring'
    );

    return this.mapResultsToScores(result, projects);
  }

  private async scoreInBatches(
    projects: ProjectItem[],
    job: Job,
    batchSize: number,
    timeoutMs: number
  ): Promise<ProjectScore[]> {
    const allScores: ProjectScore[] = [];
    for (let i = 0; i < projects.length; i += batchSize) {
      const batch = projects.slice(i, i + batchSize);
      const batchScores = await this.scoreBatch(batch, job, timeoutMs);
      allScores.push(...batchScores);
    }
    return allScores;
  }

  private mapResultsToScores(
    result: ProjectScoreResult,
    projects: ProjectItem[]
  ): ProjectScore[] {
    const scores: ProjectScore[] = [];

    for (const scoreResult of result.scores) {
      const project = projects.find((p) => p.id === scoreResult.projectId);
      if (project) {
        scores.push({
          project,
          relevanceScore: scoreResult.relevanceScore,
          reasoning: scoreResult.reasoning,
        });
      }
    }

    // default scores for any not returned
    for (const project of projects) {
      if (!scores.find((s) => s.project.id === project.id)) {
        scores.push({
          project,
          relevanceScore: 3,
          reasoning: 'Unable to analyze - using default score',
        });
      }
    }

    return scores;
  }

  private createDefaultScores(projects: ProjectItem[]): ProjectScore[] {
    return projects.map((project) => ({
      project,
      relevanceScore: 3,
      reasoning: 'AI analysis unavailable - using default score',
    }));
  }

  private estimateTotalTokens(projects: ProjectItem[], job: Job): number {
    let tokenCount = 0;

    tokenCount += (job.title?.length || 0) / 4;
    tokenCount += (job.description?.length || 0) / 4;

    if (job.skills) {
      tokenCount += job.skills.reduce(
        (sum, skill) => sum + (skill.name?.length || 0) / 4,
        0
      );
    }

    for (const prj of projects) {
      tokenCount += (prj.name?.length || 0) / 4;
      tokenCount += (prj.description?.length || 0) / 4;
      if (prj.highlights) tokenCount += prj.highlights.join(' ').length / 4;
      if (prj.keywords) tokenCount += prj.keywords.join(' ').length / 4;
      if (prj.roles) tokenCount += prj.roles.join(' ').length / 4;
      if (prj.entity) tokenCount += (prj.entity.length || 0) / 4;
      tokenCount += (prj.type?.length || 0) / 4;
    }

    return Math.ceil(tokenCount);
  }
}
