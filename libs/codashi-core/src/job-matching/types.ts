import { ExperienceAnalysisOptions } from './experience/types';
import { ProjectAnalysisOptions } from './projects/types';
import { SkillAnalysisOptions } from './skills/types';
import { TitleAnalysisOptions } from './title/utils';

/**
 * Options for configuring the complete single-resume analysis
 */
export type AnalysisOptions = {
  /** Experience analysis options */
  experience?: ExperienceAnalysisOptions;
  /** Skills analysis options */
  skills?: SkillAnalysisOptions;
  /** Title analysis options */
  title?: TitleAnalysisOptions;
  /** Projects analysis options */
  projects?: ProjectAnalysisOptions;
  /** Whether to include detailed source information */
  includeSourceDetails?: boolean;
  /** Overall timeout for the complete analysis */
  timeoutMs?: number;
};
